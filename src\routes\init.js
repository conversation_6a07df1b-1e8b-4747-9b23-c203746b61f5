// @ts-check
import { readBody } from 'h3';
import BrowserManager from '../utils/browser.ts';
import { getVerificationCode } from '../utils/emailHelper.ts';
import config from '../config.js';

/**
 * @typedef {Object} AccountData
 * @property {string} email - 账号邮箱
 * @property {string} password - 账号密码
 * @property {number} [initStatus] - 初始化状态：1-成功，2-失败
 * @property {string} [initDatetime] - 初始化时间
 * @property {string} [initFailMsg] - 初始化失败消息
 */

/**
 * @typedef {Object} InitResult
 * @property {boolean} success - 操作是否成功
 */

/**
 * @typedef {Object} InitResponse
 * @property {boolean} success - 整体操作是否成功
 * @property {string} message - 响应消息
 * @property {AccountData[]} accounts - 处理后的账号数据
 * @property {Object} summary - 处理结果摘要
 * @property {number} summary.total - 总账号数
 * @property {number} summary.success - 成功账号数
 * @property {number} summary.failed - 失败账号数
 * @property {string} [error] - 错误信息（仅在失败时）
 */

/**
 * Microsoft账号初始化任务类
 */
class MicrosoftAccountInitTask {
    /**
     * 构造函数
     */
    constructor() {
        /** @type {Console} */
        this.logger = console;
    }

    /**
     * 处理单个账号的初始化
     * @param {import('playwright').Page} page - Playwright页面对象
     * @param {string} email - 账号邮箱
     * @param {string} password - 账号密码
     * @param {number} index - 当前账号索引
     * @param {number} total - 总账号数
     * @returns {Promise<InitResult>} 初始化结果
     */
    async handleSingleAccount(page, email, password, index, total) {
        const maxRetries = 3;
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                this.logger.info(`开始执行初始化任务 [${index}/${total}]: ${email} (尝试 ${retryCount + 1})`);

                // 清除所有cookies
                await page.context().clearCookies();

                // 访问奖励页面
                await page.goto("https://rewards.bing.com");

                // 输入邮箱
                await page.waitForURL('https://login.live.com/oauth20_authorize.srf**', { timeout: 3000 });
                await page.fill('input[type="email"]', email);
                await page.click('button[type="submit"]');

                try {
                    await page.waitForSelector('#idA_PWD_SwitchToPassword', { timeout: 3000 });
                    await page.click('#idA_PWD_SwitchToPassword');
                } catch (e) {
                    this.logger.info(`没有处理多重验证，继续执行: ${e.message}`);
                }

                // 输入密码
                await page.fill('input[type="password"]', password);
                await page.fill('input[type="password"]', password);
                await page.click('button[type="submit"]');

                // 检查密码错误
                try {
                    await page.waitForURL('https://login.live.com/ppsecure/**', { timeout: 3000 });
                    await page.waitForSelector("#field-8__validationMessage", { timeout: 5000 });
                    throw new Error("密码错误");
                } catch (e) {
                    if (e.message.includes("密码错误")) {
                        throw new Error("密码错误");
                    }
                    this.logger.info(`密码正确，继续执行: ${e.message}`);
                }

                // 检查账号被锁定
                let serviceAbuseLanding = false;
                try {
                    await page.waitForURL(url => String(url).startsWith('https://account.live.com/Abuse'), { timeout: 5000 });
                    serviceAbuseLanding = true;
                } catch (e) {
                    this.logger.info(`没有账号锁定页面，继续执行: ${e.message}`);
                }

                if (serviceAbuseLanding) {
                    throw new Error("账号被锁定");
                }

                // 处理账号恢复页面
                try {
                    await page.waitForURL(url => String(url).startsWith('https://account.live.com/recover'), { timeout: 5000 });
                    await page.click('input[type="submit"]');

                    const proofEmailConfig = config.app.proof[0];
                    const proofDomain = proofEmailConfig.suffix;
                    const proofApi = proofEmailConfig.apiUrl;
                    const calculatedProofEmail = `${email.split('@')[0]}@${proofDomain}`;

                    await page.waitForSelector("#iProofEmail", { timeout: 3000 });
                    await page.fill("#iProofEmail", calculatedProofEmail);
                    const timestamp = Math.floor(Date.now() / 1000);
                    await page.click('input[type="submit"]');

                    // 获取验证码
                    const verificationCode = await getVerificationCode(proofApi, proofEmailConfig.token || '', calculatedProofEmail, timestamp);
                    await page.fill('input[type="tel"]', verificationCode);
                    await page.click('input[type="submit"]');
                } catch (e) {
                    this.logger.info(`没有帮助我们保护帐户页面，继续执行: ${/** @type {Error} */(e).message}`);
                }

                // 处理多重验证
                try {
                    await page.waitForURL('https://account.live.com/proofs/**', { timeout: 3000 });
                    const proofEmailConfig = config.app.proof[0];
                    const proofDomain = proofEmailConfig.suffix;
                    const proofApi = proofEmailConfig.apiUrl;
                    const calculatedProofEmail = `${email.split('@')[0]}@${proofDomain}`;

                    let hasOption = false;
                    try {
                        await page.waitForSelector("#iProof0", { timeout: 1000 });
                        hasOption = true;
                    } catch (e) {
                        this.logger.info(`没有选择email: ${/** @type {Error} */(e).message}`);
                    }

                    const timestamp = Math.floor(Date.now() / 1000);
                    if (hasOption) {
                        await page.click('#iProof0');
                        await page.click('input[type="submit"]');
                    } else {
                        await page.fill("#EmailAddress", calculatedProofEmail);
                        await page.click('input[type="submit"]');
                    }

                    // 获取验证码
                    const verificationCode = await getVerificationCode(proofApi, proofEmailConfig.token || '', calculatedProofEmail, timestamp);
                    await page.fill('input[type="tel"]', verificationCode);
                    await page.click('input[type="submit"]');
                } catch (e) {
                    this.logger.info(`没有处理多重验证，继续执行: ${/** @type {Error} */(e).message}`);
                }

                // 处理多重验证2
                for (let i = 0; i < 2; i++) {
                    try {
                        await page.waitForURL('https://account.live.com/identity/**', { timeout: 3000 });
                        const proofEmailConfig = config.app.proof[0];
                        const proofDomain = proofEmailConfig.suffix;
                        const proofApi = proofEmailConfig.apiUrl;
                        const calculatedProofEmail = `${email.split('@')[0]}@${proofDomain}`;

                        try {
                            await page.waitForSelector("#iProof0", { timeout: 1000 });
                            await page.click('#iProof0');
                        } catch (e) {
                            this.logger.info(`没有选择email: ${/** @type {Error} */(e).message}`);
                        }

                        await page.fill("#iProofEmail", calculatedProofEmail);
                        const timestamp = Math.floor(Date.now() / 1000);
                        await page.click('input[type="submit"]');

                        // 获取验证码
                        const verificationCode = await getVerificationCode(proofApi, proofEmailConfig.token || '', calculatedProofEmail, timestamp);
                        await page.fill('input[type="tel"]', verificationCode);
                        await page.click('input[type="submit"]');

                    } catch (e) {
                        this.logger.info(`没有多重验证，继续执行: ${/** @type {Error} */(e).message}`);
                    }
                }

                // 处理通行密钥提示
                try {
                    await page.waitForURL('https://account.live.com/interrupt/**', { timeout: 3000 });
                    await page.click('div[data-testid="textButtonContainer"] > div:first-child > button[type="button"]');
                } catch (e) {
                    this.logger.info(`无通行密钥软件提示，继续执行: ${/** @type {Error} */(e).message}`);
                }

                // 确认登录
                try {
                    await page.waitForURL('https://login.live.com/ppsecure/**', { timeout: 3000 });
                    await page.click('button[type="submit"]');
                } catch (e) {
                    this.logger.info(`无登录确认，继续执行: ${/** @type {Error} */(e).message}`);
                }

                // 确认登录有时出现
                try {
                    await page.waitForURL('https://login.live.com/oauth20_authorize.srf**', { timeout: 3000 });
                    await page.click('button[type="submit"]');
                } catch (e) {
                    this.logger.info(`无登录确认，继续执行: ${/** @type {Error} */(e).message}`);
                }

                try {
                    await page.waitForURL('https://login.live.com/oauth20_authorize.srf**', { timeout: 3000 });
                    await page.click('button[type="submit"]');
                } catch (e) {
                    this.logger.info(`无登录确认，继续执行: ${/** @type {Error} */(e).message}`);
                }

                try {
                    await page.waitForURL(url => String(url).startsWith('https://rewards.bing.com'), { timeout: 5000 });
                    await page.evaluate(() => {
                        const cookieContainer = /** @type {HTMLElement | null} */(document.querySelector('#cookieConsentContainer'));
                        if (cookieContainer) {
                            cookieContainer.style.display = 'none';
                        }
                    });
                    await page.waitForTimeout(1000);
                } catch (e) {
                    this.logger.info(`没有到达奖励页面，继续执行: ${/** @type {Error} */(e).message}`);
                }

                // 处理 cookie 同意弹窗
                try {
                    const cookieConsent = page.locator('#cookieConsentContainer');
                    if (await cookieConsent.isVisible({ timeout: 1000 })) {
                        // 点击"接受"按钮（第一个按钮）
                        const acceptButton = cookieConsent.locator('button').first();
                        await acceptButton.click();
                        await page.waitForTimeout(1000);
                    }
                } catch (e) {
                    this.logger.info(`${email} 处理 cookie 同意弹窗失败: ${/** @type {Error} */(e).message}`);
                }

                // 处理引导页面
                try {
                    await page.waitForSelector('#welcome-tour', { timeout: 5000 });
                    await page.click('#welcome-tour #fre-next-button:visible');
                    await page.waitForTimeout(3000 + Math.random() * 2000);
                    await page.click('#welcome-tour #fre-next-button:visible');

                    await page.waitForSelector('#welcome-tour .interest-buttons:visible', { timeout: 3000 });
                    // 获取所有符合条件的按钮
                    const buttons = await page.locator("#welcome-tour .interest-button:visible").all();
                    if (buttons.length > 0) {
                        // 随机选择一个按钮
                        const selectedButton = buttons[Math.floor(Math.random() * buttons.length)];
                        await selectedButton.click();
                    } else {
                        this.logger.info("未找到匹配的引导按钮");
                    }
                    await page.click('#welcome-tour #claim-button:visible');
                } catch (e) {
                    this.logger.info(`没有引导页面，继续执行: ${/** @type {Error} */(e).message}`);
                }

                // 检查错误
                let hasError = false;
                try {
                    await page.waitForSelector('#fraudErrorBody', { timeout: 3000 });
                    hasError = true;
                } catch (e) {
                    this.logger.info(`没有错误，继续执行: ${/** @type {Error} */(e).message}`);
                }

                if (hasError) {
                    throw new Error("Fraud error detected");
                }

                // 处理必应应用遮罩
                try {
                    await page.waitForSelector('.maybe-later', { timeout: 5000 });
                    await page.click('.maybe-later');
                } catch (e) {
                    this.logger.info(`没有必应应用遮罩，继续执行: ${/** @type {Error} */(e).message}`);
                }

                await page.waitForSelector("#daily-sets");
                const modeOn = page.locator("#daily-sets #balanceToolTipDiv #ModeOn.toggleOff");
                if (await modeOn.isVisible()) {
                    await page.click('#daily-sets #balanceToolTipDiv #ModeOn');
                }

                await page.waitForTimeout(3000 + Math.random() * 2000);

                this.logger.info(`完成初始化 ${email}`);
                return { success: true };

            } catch (error) {
                retryCount++;
                this.logger.error(`初始化任务尝试 ${retryCount} 失败 ${email}: ${/** @type {Error} */(error).message}`);

                if (retryCount >= maxRetries) {
                    this.logger.error(`达到最大重试次数 ${email}`);
                    throw new Error(`Failed after ${maxRetries} attempts: ${/** @type {Error} */(error).message}`);
                }

                await page.waitForTimeout(5000 + Math.random() * 5000);
                continue;
            }
        }

        // 如果所有重试都失败了，抛出错误
        throw new Error(`All ${maxRetries} attempts failed for ${email}`);
    }
}

/**
 * 处理初始化请求的主函数
 * @param {import('h3').H3Event} event - H3 事件对象
 * @returns {Promise<InitResponse>} 包含初始化结果的响应对象
 */
export async function initHandler(event) {
    try {
        const accountsData = await readBody(event);

        if (!Array.isArray(accountsData) || accountsData.length === 0) {
            throw new Error('Invalid request: accounts array is required');
        }

        /** @type {AccountData[]} */
        const accounts = accountsData;

        const task = new MicrosoftAccountInitTask();
        const browser = await BrowserManager.getInstance();

        // 创建浏览器上下文和页面
        const context = await browser.newContext({
            ignoreHTTPSErrors: true,
            viewport: { width: 375, height: 812 },
            isMobile: true,
            userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15'
        });
        const page = await context.newPage();

        /** @type {AccountData[]} */
        const results = [];

        try {
            for (let i = 0; i < accounts.length; i++) {
                const account = accounts[i];

                try {
                    await task.handleSingleAccount(page, account.email, account.password, i + 1, accounts.length);

                    // 成功更新状态
                    account.initStatus = 1;
                    account.initFailMsg = "";

                    console.log(`账号 ${account.email} 初始化成功`);
                } catch (error) {
                    // 失败更新状态
                    account.initStatus = 2;
                    account.initFailMsg = /** @type {Error} */(error).message;

                    console.error(`账号 ${account.email} 初始化失败: ${/** @type {Error} */(error).message}`);
                }

                results.push(account);

                // 添加随机延迟
                if (i < accounts.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, Math.random() * 3000 + 2000));
                }
            }
        } finally {
            await page.close();
            await context.close();
        }

        // 发送结果到远程服务器
        try {
            const response = await fetch('https://seedlog.godgodgame.com/newaccount/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.app.logToken}`
                },
                body: JSON.stringify(results)
            });

            if (!response.ok) {
                console.error(`Failed to send results to remote server: ${response.statusText}`);
            } else {
                console.log('Results sent to remote server successfully');
            }
        } catch (error) {
            console.error(`Error sending results to remote server: ${/** @type {Error} */(error).message}`);
        }

        return {
            success: true,
            message: 'Account initialization process completed',
            accounts: results,
            summary: {
                total: accounts.length,
                success: results.filter(r => r.initStatus === 1).length,
                failed: results.filter(r => r.initStatus === 2).length
            }
        };

    } catch (error) {
        console.error('Init handler error:', error);
        return {
            success: false,
            message: `Internal server error: ${/** @type {Error} */(error).message}`,
            error: /** @type {Error} */(error).message,
            accounts: [],
            summary: {
                total: 0,
                success: 0,
                failed: 0
            }
        };
    }
}